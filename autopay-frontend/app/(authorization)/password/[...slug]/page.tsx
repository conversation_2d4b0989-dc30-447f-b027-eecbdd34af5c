'use client'

import { Icons } from '@/components/icons'
import { useDomainType } from '@/lib/hooks/useDomainType'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useRouter, useSearchParams } from 'next/navigation'
import { use, useEffect, useState } from 'react'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'

interface ComponentProps {
  params:
    | {
        slug: string[]
      }
    | Promise<{ slug: string[] }>
}

export default function Component({ params }: ComponentProps) {
  const resolvedParams = params instanceof Promise ? use(params) : params

  const router = useRouter()
  const searchParams = useSearchParams()
  const { isMappedDomain } = useDomainType()
  const verificationUrl = resolvedParams.slug.join('/') + '?' + searchParams.toString()

  const [reset, setVerified] = useState(false)
  const [data, setData] = useState<ApiResponse>()

  const { isPending, mutate } = useMutation({
    mutationFn: (verificationUrl: string) => {
      // Use different API endpoints based on domain type
      const endpoint = isMappedDomain ? '/customer/password/' : '/password/'

      return queryFetchHelper(endpoint + verificationUrl, {
        method: 'GET',
      })
    },
    onSuccess: () => {
      router.push('/login')
      const message = isMappedDomain
        ? 'Mật khẩu khách hàng của bạn đã được đặt lại thành công. Vui lòng kiểm tra email để nhận mật khẩu mới.'
        : 'Mật khẩu của bạn đã được đặt lại thành công. Vui lòng kiểm tra email để nhận mật khẩu mới.'
      toast.success(message)
    },
    onError: (error: ApiResponse) => {
      setData(error)
      toast.error(error.message)
    },
    onSettled: (data) => {
      setVerified(data?.success ?? false)
      if (data) {
        setData(data)
      }
    },
  })

  useEffect(() => {
    mutate(verificationUrl)
  }, [mutate, verificationUrl])

  return (
    <div className="flex flex-col">
      <h1 className="text-center text-4xl font-extrabold tracking-tight">Đặt Lại Mật Khẩu</h1>
      <div className="text-muted-foreground mt-6 text-center">
        {isPending && (
          <>
            <Icons.spinner className="mr-1 inline-block h-6 w-6 animate-spin" />
            Đang xử lý...
          </>
        )}
        {reset && (
          <>
            <Icons.spinner className="mr-1 inline-block h-6 w-6 animate-spin" />
            Đang chuyển hướng...
          </>
        )}
        {!reset && data && (
          <Alert
            variant="destructive"
            className="mx-auto w-fit text-center">
            <AlertDescription>{data.message || 'Đã xảy ra lỗi khi đặt lại mật khẩu của bạn.'}</AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  )
}
